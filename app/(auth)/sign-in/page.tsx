"use client";

import {
  PasswordInput,
  RadioCheckInput,
  TextInput,
} from "@/components/FormInputs";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "flowbite-react";
import Image from "next/image";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod";

export default function SignIn() {
  const formSchema = z.object({
    email: z.string().email(),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
    remember: z.boolean(),
  });
  // 1. Define your form.
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      remember: false,
    },
  });

  return (
    <section className="pt:mt-0 mx-auto flex flex-col items-center justify-center px-6 pt-8 md:h-screen dark:bg-gray-900">
      <a
        href="https://flowbite-admin-dashboard.vercel.app/"
        className="mb-8 flex items-center justify-center text-2xl font-semibold lg:mb-10 dark:text-white"
      >
        <Image
          src="https://flowbite-admin-dashboard.vercel.app/images/logo.svg"
          width={100}
          height={100}
          className="mr-4 h-11"
          alt="FlowBite Logo"
        />
        <span>Flowbite</span>
      </a>
      {/* <!-- Card --> */}
      <div className="w-full max-w-xl space-y-8 rounded-lg bg-white p-6 shadow sm:p-8 dark:bg-gray-800">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Sign in
        </h2>
        <form method="POST" className="mt-8 space-y-6" action="">
          <div>
            <TextInput
              control={form.control}
              name="email"
              type="email"
              label="Your email"
              placeholder="<EMAIL>"
              rules={{ required: "Email is required" }}
            />
          </div>
          <div>
            <PasswordInput
              control={form.control}
              name="password"
              label="Your password"
              placeholder="••••••••"
              rules={{ required: "Password is required" }}
            />
          </div>
          <div className="flex items-start sm:pt-2 sm:pb-2">
            <RadioCheckInput
              control={form.control}
              name="remember"
              type="checkbox"
              value="remember"
              label="Remember me"
            />
          </div>

          <Button
            type="submit"
            color="primary"
            className="bg-primary-700 hover:bg-primary-800 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 w-full rounded-lg px-5 py-3 text-center text-base font-medium text-white focus:ring-4 sm:w-auto"
          >
            Sign in
          </Button>
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Don&apos;t have an account? &nbsp;
            <Link
              href="/sign-up"
              className="text-primary-700 dark:text-primary-500 hover:underline"
            >
              Sign up
            </Link>
          </div>
        </form>
      </div>
    </section>
  );
}
