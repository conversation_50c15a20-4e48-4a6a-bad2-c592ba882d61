"use client";

import { forwardRef, useCallback, useEffect, useState } from "react";
import { cn } from "./utils";

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface BaseInputProps {
  id?: string;
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  error?: string;
  helpText?: string;
}

interface InputProps extends BaseInputProps {
  type?:
    | "text"
    | "email"
    | "password"
    | "number"
    | "tel"
    | "url"
    | "search"
    | "date"
    | "time"
    | "datetime-local";
  value?: string;
  defaultValue?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  autoComplete?: string;
  min?: number;
  max?: number;
  step?: number;
  pattern?: string;
  maxLength?: number;
  minLength?: number;
}

interface TextareaProps extends BaseInputProps {
  value?: string;
  defaultValue?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  rows?: number;
  cols?: number;
  resize?: "none" | "both" | "horizontal" | "vertical";
}

interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

interface SelectProps extends BaseInputProps {
  value?: string | number;
  defaultValue?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  options: SelectOption[];
  multiple?: boolean;
}

interface CheckboxProps extends Omit<BaseInputProps, "placeholder"> {
  checked?: boolean;
  defaultChecked?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  value?: string;
}

interface ButtonProps {
  children: React.ReactNode;
  type?: "button" | "submit" | "reset";
  variant?:
    | "primary"
    | "secondary"
    | "danger"
    | "success"
    | "warning"
    | "ghost";
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  loading?: boolean;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  className?: string;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
}

interface CurrencyInputProps
  extends Omit<InputProps, "type" | "value" | "onChange"> {
  value?: number;
  onChange?: (value: number | undefined) => void;
  currency?: string;
  locale?: string;
  allowNegative?: boolean;
  maxValue?: number;
  minValue?: number;
}

interface SearchInputProps extends Omit<InputProps, "type" | "onChange"> {
  onSearch?: (query: string) => void;
  onClear?: () => void;
  debounceMs?: number;
  showClearButton?: boolean;
  searchIcon?: React.ReactNode;
  clearIcon?: React.ReactNode;
}

interface FormGroupProps {
  children: React.ReactNode;
  className?: string;
}

interface FormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

interface FormGridProps {
  children: React.ReactNode;
  cols?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: "sm" | "md" | "lg";
  className?: string;
}

interface FormCardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
}

interface FormActionsProps {
  children: React.ReactNode;
  align?: "left" | "right" | "center" | "between";
  className?: string;
}

interface FormDividerProps {
  label?: string;
  className?: string;
}

// ============================================================================
// CORE INPUT COMPONENTS
// ============================================================================

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      id,
      name,
      label,
      type = "text",
      placeholder,
      required = false,
      disabled = false,
      className = "",
      error,
      helpText,
      value,
      defaultValue,
      onChange,
      onBlur,
      autoComplete,
      min,
      max,
      step,
      pattern,
      maxLength,
      minLength,
      ...props
    },
    ref,
  ) => {
    const inputId = id || name;
    const hasError = !!error;

    const baseClasses = [
      "block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset",
      "placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6",
      "transition-colors duration-200",
      "dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400",
    ];

    const stateClasses = hasError
      ? [
          "ring-red-300 focus:ring-red-500 dark:ring-red-600 dark:focus:ring-red-500",
          "text-red-900 dark:text-red-100",
        ]
      : [
          "ring-gray-300 focus:ring-blue-500 dark:ring-gray-600 dark:focus:ring-blue-500",
        ];

    const disabledClasses = disabled
      ? [
          "bg-gray-50 text-gray-500 cursor-not-allowed",
          "dark:bg-gray-800 dark:text-gray-400",
        ]
      : ["bg-white dark:bg-gray-700"];

    const inputClasses = cn(
      ...baseClasses,
      ...stateClasses,
      ...disabledClasses,
      className,
    );

    return (
      <div className="space-y-1">
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              "block text-sm leading-6 font-medium",
              hasError
                ? "text-red-700 dark:text-red-400"
                : "text-gray-900 dark:text-gray-100",
              required && "after:ml-0.5 after:text-red-500 after:content-['*']",
            )}
          >
            {label}
          </label>
        )}

        <div className="relative">
          <input
            ref={ref}
            id={inputId}
            name={name}
            type={type}
            value={value}
            defaultValue={defaultValue}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            autoComplete={autoComplete}
            min={min}
            max={max}
            step={step}
            pattern={pattern}
            maxLength={maxLength}
            minLength={minLength}
            className={inputClasses}
            onChange={onChange}
            onBlur={onBlur}
            aria-invalid={hasError}
            aria-describedby={
              error
                ? `${inputId}-error`
                : helpText
                  ? `${inputId}-help`
                  : undefined
            }
            {...props}
          />
        </div>

        {error && (
          <p
            id={`${inputId}-error`}
            className="text-sm text-red-600 dark:text-red-400"
            role="alert"
          >
            {error}
          </p>
        )}

        {helpText && !error && (
          <p
            id={`${inputId}-help`}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {helpText}
          </p>
        )}
      </div>
    );
  },
);

Input.displayName = "Input";

// Specialized input variants
export const EmailInput = forwardRef<
  HTMLInputElement,
  Omit<InputProps, "type">
>((props, ref) => (
  <Input ref={ref} type="email" autoComplete="email" {...props} />
));

EmailInput.displayName = "EmailInput";

export const PasswordInput = forwardRef<
  HTMLInputElement,
  Omit<InputProps, "type">
>((props, ref) => (
  <>
  <Input ref={ref} type={showPassword ? "text" : "password"} autoComplete="current-password" {...props} />
  <button
    type="button"
    onClick={() => setShowPassword((prev) => !prev)}
    className="absolute inset-y-0 right-2 flex items-center text-gray-500 dark:text-gray-300"
    tabIndex={-1}
  >
    {showPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
  </button>
  </>
));

PasswordInput.displayName = "PasswordInput";

export const NumberInput = forwardRef<
  HTMLInputElement,
  Omit<InputProps, "type">
>((props, ref) => <Input ref={ref} type="number" {...props} />);

NumberInput.displayName = "NumberInput";

// ============================================================================
// SELECT COMPONENT
// ============================================================================

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      id,
      name,
      label,
      placeholder,
      required = false,
      disabled = false,
      className = "",
      error,
      helpText,
      value,
      defaultValue,
      onChange,
      onBlur,
      options,
      multiple = false,
      ...props
    },
    ref,
  ) => {
    const selectId = id || name;
    const hasError = !!error;

    const baseClasses = [
      "block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset",
      "focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6",
      "transition-colors duration-200",
      "dark:bg-gray-700 dark:text-white",
    ];

    const stateClasses = hasError
      ? [
          "ring-red-300 focus:ring-red-500 dark:ring-red-600 dark:focus:ring-red-500",
          "text-red-900 dark:text-red-100",
        ]
      : [
          "ring-gray-300 focus:ring-blue-500 dark:ring-gray-600 dark:focus:ring-blue-500",
        ];

    const disabledClasses = disabled
      ? [
          "bg-gray-50 text-gray-500 cursor-not-allowed",
          "dark:bg-gray-800 dark:text-gray-400",
        ]
      : ["bg-white dark:bg-gray-700"];

    const selectClasses = cn(
      ...baseClasses,
      ...stateClasses,
      ...disabledClasses,
      className,
    );

    return (
      <div className="space-y-1">
        {label && (
          <label
            htmlFor={selectId}
            className={cn(
              "block text-sm leading-6 font-medium",
              hasError
                ? "text-red-700 dark:text-red-400"
                : "text-gray-900 dark:text-gray-100",
              required && "after:ml-0.5 after:text-red-500 after:content-['*']",
            )}
          >
            {label}
          </label>
        )}

        <div className="relative">
          <select
            ref={ref}
            id={selectId}
            name={name}
            value={value}
            defaultValue={defaultValue}
            required={required}
            disabled={disabled}
            multiple={multiple}
            className={selectClasses}
            onChange={onChange}
            onBlur={onBlur}
            aria-invalid={hasError}
            aria-describedby={
              error
                ? `${selectId}-error`
                : helpText
                  ? `${selectId}-help`
                  : undefined
            }
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {error && (
          <p
            id={`${selectId}-error`}
            className="text-sm text-red-600 dark:text-red-400"
            role="alert"
          >
            {error}
          </p>
        )}

        {helpText && !error && (
          <p
            id={`${selectId}-help`}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {helpText}
          </p>
        )}
      </div>
    );
  },
);

Select.displayName = "Select";

// Specialized select components
export const CategorySelect = forwardRef<
  HTMLSelectElement,
  Omit<SelectProps, "options">
>((props, ref) => {
  const categoryOptions: SelectOption[] = [
    { value: "fruits", label: "Fruits" },
    { value: "vegetables", label: "Vegetables" },
    { value: "dairy", label: "Dairy" },
    { value: "bakery", label: "Bakery" },
    { value: "beverages", label: "Beverages" },
    { value: "meat", label: "Meat & Poultry" },
    { value: "frozen", label: "Frozen Foods" },
    { value: "snacks", label: "Snacks" },
    { value: "household", label: "Household Items" },
    { value: "personal-care", label: "Personal Care" },
  ];

  return (
    <Select
      ref={ref}
      options={categoryOptions}
      placeholder="Select category"
      {...props}
    />
  );
});

CategorySelect.displayName = "CategorySelect";

export const CurrencySelect = forwardRef<
  HTMLSelectElement,
  Omit<SelectProps, "options">
>((props, ref) => {
  const currencyOptions: SelectOption[] = [
    { value: "USD", label: "USD - US Dollar" },
    { value: "EUR", label: "EUR - Euro" },
    { value: "GBP", label: "GBP - British Pound" },
    { value: "CAD", label: "CAD - Canadian Dollar" },
    { value: "AUD", label: "AUD - Australian Dollar" },
    { value: "JPY", label: "JPY - Japanese Yen" },
  ];

  return (
    <Select
      ref={ref}
      options={currencyOptions}
      placeholder="Select currency"
      {...props}
    />
  );
});

CurrencySelect.displayName = "CurrencySelect";

// ============================================================================
// TEXTAREA COMPONENT
// ============================================================================

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      id,
      name,
      label,
      placeholder,
      required = false,
      disabled = false,
      className = "",
      error,
      helpText,
      value,
      defaultValue,
      onChange,
      onBlur,
      rows = 3,
      cols,
      resize = "vertical",
      ...props
    },
    ref,
  ) => {
    const textareaId = id || name;
    const hasError = !!error;

    const baseClasses = [
      "block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset",
      "placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6",
      "transition-colors duration-200",
      "dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400",
    ];

    const stateClasses = hasError
      ? [
          "ring-red-300 focus:ring-red-500 dark:ring-red-600 dark:focus:ring-red-500",
          "text-red-900 dark:text-red-100",
        ]
      : [
          "ring-gray-300 focus:ring-blue-500 dark:ring-gray-600 dark:focus:ring-blue-500",
        ];

    const disabledClasses = disabled
      ? [
          "bg-gray-50 text-gray-500 cursor-not-allowed",
          "dark:bg-gray-800 dark:text-gray-400",
        ]
      : ["bg-white dark:bg-gray-700"];

    const resizeClasses = {
      none: "resize-none",
      both: "resize",
      horizontal: "resize-x",
      vertical: "resize-y",
    };

    const textareaClasses = cn(
      ...baseClasses,
      ...stateClasses,
      ...disabledClasses,
      resizeClasses[resize],
      className,
    );

    return (
      <div className="space-y-1">
        {label && (
          <label
            htmlFor={textareaId}
            className={cn(
              "block text-sm leading-6 font-medium",
              hasError
                ? "text-red-700 dark:text-red-400"
                : "text-gray-900 dark:text-gray-100",
              required && "after:ml-0.5 after:text-red-500 after:content-['*']",
            )}
          >
            {label}
          </label>
        )}

        <div className="relative">
          <textarea
            ref={ref}
            id={textareaId}
            name={name}
            value={value}
            defaultValue={defaultValue}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            rows={rows}
            cols={cols}
            className={textareaClasses}
            onChange={onChange}
            onBlur={onBlur}
            aria-invalid={hasError}
            aria-describedby={
              error
                ? `${textareaId}-error`
                : helpText
                  ? `${textareaId}-help`
                  : undefined
            }
            {...props}
          />
        </div>

        {error && (
          <p
            id={`${textareaId}-error`}
            className="text-sm text-red-600 dark:text-red-400"
            role="alert"
          >
            {error}
          </p>
        )}

        {helpText && !error && (
          <p
            id={`${textareaId}-help`}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {helpText}
          </p>
        )}
      </div>
    );
  },
);

Textarea.displayName = "Textarea";

// ============================================================================
// CHECKBOX COMPONENT
// ============================================================================

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  (
    {
      id,
      name,
      label,
      required = false,
      disabled = false,
      className = "",
      error,
      helpText,
      checked,
      defaultChecked,
      onChange,
      onBlur,
      value,
      ...props
    },
    ref,
  ) => {
    const checkboxId = id || name;
    const hasError = !!error;

    const baseClasses = [
      "h-4 w-4 rounded border-gray-300 text-blue-600",
      "focus:ring-blue-500 focus:ring-2 focus:ring-offset-2",
      "transition-colors duration-200",
      "dark:border-gray-600 dark:bg-gray-700 dark:focus:ring-blue-600 dark:focus:ring-offset-gray-800",
    ];

    const stateClasses = hasError
      ? ["border-red-300 dark:border-red-600"]
      : ["border-gray-300 dark:border-gray-600"];

    const disabledClasses = disabled
      ? ["opacity-50 cursor-not-allowed"]
      : ["cursor-pointer"];

    const checkboxClasses = cn(
      ...baseClasses,
      ...stateClasses,
      ...disabledClasses,
      className,
    );

    return (
      <div className="space-y-1">
        <div className="flex items-start">
          <div className="flex h-5 items-center">
            <input
              ref={ref}
              id={checkboxId}
              name={name}
              type="checkbox"
              value={value}
              checked={checked}
              defaultChecked={defaultChecked}
              required={required}
              disabled={disabled}
              className={checkboxClasses}
              onChange={onChange}
              onBlur={onBlur}
              aria-invalid={hasError}
              aria-describedby={
                error
                  ? `${checkboxId}-error`
                  : helpText
                    ? `${checkboxId}-help`
                    : undefined
              }
              {...props}
            />
          </div>
          {label && (
            <div className="ml-3 text-sm">
              <label
                htmlFor={checkboxId}
                className={cn(
                  "cursor-pointer font-medium",
                  hasError
                    ? "text-red-700 dark:text-red-400"
                    : "text-gray-900 dark:text-gray-100",
                  disabled && "cursor-not-allowed opacity-50",
                  required &&
                    "after:ml-0.5 after:text-red-500 after:content-['*']",
                )}
              >
                {label}
              </label>
            </div>
          )}
        </div>

        {error && (
          <p
            id={`${checkboxId}-error`}
            className="ml-7 text-sm text-red-600 dark:text-red-400"
            role="alert"
          >
            {error}
          </p>
        )}

        {helpText && !error && (
          <p
            id={`${checkboxId}-help`}
            className="ml-7 text-sm text-gray-500 dark:text-gray-400"
          >
            {helpText}
          </p>
        )}
      </div>
    );
  },
);

Checkbox.displayName = "Checkbox";

// ============================================================================
// BUTTON COMPONENT
// ============================================================================

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      type = "button",
      variant = "primary",
      size = "md",
      disabled = false,
      loading = false,
      onClick,
      className = "",
      icon,
      iconPosition = "left",
      ...props
    },
    ref,
  ) => {
    const baseClasses = [
      "inline-flex items-center justify-center font-medium rounded-md",
      "focus:outline-none focus:ring-2 focus:ring-offset-2",
      "transition-all duration-200",
      "disabled:opacity-50 disabled:cursor-not-allowed",
    ];

    const sizeClasses = {
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-sm",
      lg: "px-6 py-3 text-base",
    };

    const variantClasses = {
      primary: [
        "bg-blue-600 text-white shadow-sm",
        "hover:bg-blue-700 focus:ring-blue-500",
        "dark:bg-blue-600 dark:hover:bg-blue-700",
      ],
      secondary: [
        "bg-white text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300",
        "hover:bg-gray-50 focus:ring-blue-500",
        "dark:bg-gray-700 dark:text-gray-100 dark:ring-gray-600",
        "dark:hover:bg-gray-600",
      ],
      danger: [
        "bg-red-600 text-white shadow-sm",
        "hover:bg-red-700 focus:ring-red-500",
        "dark:bg-red-600 dark:hover:bg-red-700",
      ],
      success: [
        "bg-green-600 text-white shadow-sm",
        "hover:bg-green-700 focus:ring-green-500",
        "dark:bg-green-600 dark:hover:bg-green-700",
      ],
      warning: [
        "bg-yellow-600 text-white shadow-sm",
        "hover:bg-yellow-700 focus:ring-yellow-500",
        "dark:bg-yellow-600 dark:hover:bg-yellow-700",
      ],
      ghost: [
        "text-gray-700 hover:text-gray-900 hover:bg-gray-100",
        "focus:ring-gray-500",
        "dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-800",
      ],
    };

    const buttonClasses = cn(
      ...baseClasses,
      sizeClasses[size],
      ...variantClasses[variant],
      className,
    );

    const LoadingSpinner = () => (
      <svg
        className="mr-2 -ml-1 h-4 w-4 animate-spin"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    );

    return (
      <button
        ref={ref}
        type={type}
        disabled={disabled || loading}
        className={buttonClasses}
        onClick={onClick}
        {...props}
      >
        {loading && <LoadingSpinner />}
        {!loading && icon && iconPosition === "left" && (
          <span className="mr-2">{icon}</span>
        )}
        {children}
        {!loading && icon && iconPosition === "right" && (
          <span className="ml-2">{icon}</span>
        )}
      </button>
    );
  },
);

Button.displayName = "Button";

// Specialized button components
export const SubmitButton = forwardRef<
  HTMLButtonElement,
  Omit<ButtonProps, "type">
>((props, ref) => <Button ref={ref} type="submit" {...props} />);

SubmitButton.displayName = "SubmitButton";

export const ResetButton = forwardRef<
  HTMLButtonElement,
  Omit<ButtonProps, "type">
>((props, ref) => (
  <Button ref={ref} type="reset" variant="secondary" {...props} />
));

ResetButton.displayName = "ResetButton";

export const CancelButton = forwardRef<
  HTMLButtonElement,
  Omit<ButtonProps, "type" | "variant">
>((props, ref) => (
  <Button ref={ref} type="button" variant="ghost" {...props} />
));

CancelButton.displayName = "CancelButton";

export const DeleteButton = forwardRef<
  HTMLButtonElement,
  Omit<ButtonProps, "variant">
>((props, ref) => <Button ref={ref} variant="danger" {...props} />);

DeleteButton.displayName = "DeleteButton";

export const SaveButton = forwardRef<
  HTMLButtonElement,
  Omit<ButtonProps, "variant">
>((props, ref) => <Button ref={ref} variant="success" {...props} />);

SaveButton.displayName = "SaveButton";

// ============================================================================
// SPECIALIZED COMPONENTS
// ============================================================================

export const CurrencyInput = forwardRef<HTMLInputElement, CurrencyInputProps>(
  (
    {
      id,
      name,
      label,
      placeholder,
      required = false,
      disabled = false,
      className = "",
      error,
      helpText,
      value,
      onChange,
      currency = "USD",
      locale = "en-US",
      allowNegative = false,
      maxValue,
      minValue = 0,
      ...props
    },
    ref,
  ) => {
    const [displayValue, setDisplayValue] = useState("");
    const inputId = id || name;
    const hasError = !!error;

    // Format number as currency for display
    const formatCurrency = useCallback(
      (num: number): string => {
        return new Intl.NumberFormat(locale, {
          style: "currency",
          currency,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(num);
      },
      [locale, currency],
    );

    // Parse currency string to number
    const parseCurrency = (str: string): number | undefined => {
      const cleaned = str.replace(/[^\d.-]/g, "");
      const num = parseFloat(cleaned);
      return isNaN(num) ? undefined : num;
    };

    // Update display value when value prop changes
    useEffect(() => {
      if (value !== undefined) {
        setDisplayValue(formatCurrency(value));
      } else {
        setDisplayValue("");
      }
    }, [value, currency, locale, formatCurrency]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;

      if (inputValue === "") {
        setDisplayValue("");
        onChange?.(undefined);
        return;
      }

      const numericValue = parseCurrency(inputValue);

      if (numericValue !== undefined) {
        let constrainedValue = numericValue;

        if (!allowNegative && constrainedValue < 0) {
          constrainedValue = 0;
        }

        if (minValue !== undefined && constrainedValue < minValue) {
          constrainedValue = minValue;
        }

        if (maxValue !== undefined && constrainedValue > maxValue) {
          constrainedValue = maxValue;
        }

        setDisplayValue(formatCurrency(constrainedValue));
        onChange?.(constrainedValue);
      }
    };

    const handleBlur = () => {
      if (value !== undefined) {
        setDisplayValue(formatCurrency(value));
      }
    };

    const getCurrencySymbol = (): string => {
      try {
        return (
          new Intl.NumberFormat(locale, {
            style: "currency",
            currency,
          })
            .formatToParts(0)
            .find((part) => part.type === "currency")?.value || "$"
        );
      } catch {
        return "$";
      }
    };

    const baseClasses = [
      "block w-full rounded-md border-0 py-1.5 pl-8 pr-3 text-gray-900 shadow-sm ring-1 ring-inset",
      "placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6",
      "transition-colors duration-200",
      "dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400",
    ];

    const stateClasses = hasError
      ? [
          "ring-red-300 focus:ring-red-500 dark:ring-red-600 dark:focus:ring-red-500",
          "text-red-900 dark:text-red-100",
        ]
      : [
          "ring-gray-300 focus:ring-blue-500 dark:ring-gray-600 dark:focus:ring-blue-500",
        ];

    const disabledClasses = disabled
      ? [
          "bg-gray-50 text-gray-500 cursor-not-allowed",
          "dark:bg-gray-800 dark:text-gray-400",
        ]
      : ["bg-white dark:bg-gray-700"];

    const inputClasses = cn(
      ...baseClasses,
      ...stateClasses,
      ...disabledClasses,
      className,
    );

    return (
      <div className="space-y-1">
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              "block text-sm leading-6 font-medium",
              hasError
                ? "text-red-700 dark:text-red-400"
                : "text-gray-900 dark:text-gray-100",
              required && "after:ml-0.5 after:text-red-500 after:content-['*']",
            )}
          >
            {label}
          </label>
        )}

        <div className="relative">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <span className="text-gray-500 sm:text-sm dark:text-gray-400">
              {getCurrencySymbol()}
            </span>
          </div>
          <input
            ref={ref}
            id={inputId}
            name={name}
            type="text"
            value={displayValue}
            placeholder={placeholder || formatCurrency(0)}
            required={required}
            disabled={disabled}
            className={inputClasses}
            onChange={handleChange}
            onBlur={handleBlur}
            aria-invalid={hasError}
            aria-describedby={
              error
                ? `${inputId}-error`
                : helpText
                  ? `${inputId}-help`
                  : undefined
            }
            {...props}
          />
        </div>

        {error && (
          <p
            id={`${inputId}-error`}
            className="text-sm text-red-600 dark:text-red-400"
            role="alert"
          >
            {error}
          </p>
        )}

        {helpText && !error && (
          <p
            id={`${inputId}-help`}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {helpText}
          </p>
        )}
      </div>
    );
  },
);

CurrencyInput.displayName = "CurrencyInput";

export const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  (
    {
      id,
      name,
      label,
      placeholder = "Search...",
      required = false,
      disabled = false,
      className = "",
      error,
      helpText,
      value,
      defaultValue,
      onSearch,
      onClear,
      onBlur,
      debounceMs = 300,
      showClearButton = true,
      searchIcon,
      clearIcon,
      ...props
    },
    ref,
  ) => {
    const [internalValue, setInternalValue] = useState(defaultValue || "");
    const inputId = id || name;
    const hasError = !!error;
    const currentValue = value !== undefined ? value : internalValue;

    // Debounced search function
    const debouncedSearchRef = useCallback(() => {
      return debounce((query: string) => {
        onSearch?.(query);
      }, debounceMs);
    }, [onSearch, debounceMs]);

    const debouncedSearch = debouncedSearchRef();

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;

      if (value === undefined) {
        setInternalValue(newValue);
      }

      debouncedSearch(newValue);
    };

    const handleClear = () => {
      if (value === undefined) {
        setInternalValue("");
      }
      onClear?.();
      onSearch?.("");
    };

    const baseClasses = [
      "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset",
      "placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6",
      "transition-colors duration-200",
      "dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400",
    ];

    const paddingClasses = [
      "pl-10", // Space for search icon
      showClearButton && currentValue ? "pr-10" : "pr-3", // Space for clear button
    ];

    const stateClasses = hasError
      ? [
          "ring-red-300 focus:ring-red-500 dark:ring-red-600 dark:focus:ring-red-500",
          "text-red-900 dark:text-red-100",
        ]
      : [
          "ring-gray-300 focus:ring-blue-500 dark:ring-gray-600 dark:focus:ring-blue-500",
        ];

    const disabledClasses = disabled
      ? [
          "bg-gray-50 text-gray-500 cursor-not-allowed",
          "dark:bg-gray-800 dark:text-gray-400",
        ]
      : ["bg-white dark:bg-gray-700"];

    const inputClasses = cn(
      ...baseClasses,
      ...paddingClasses,
      ...stateClasses,
      ...disabledClasses,
      className,
    );

    const DefaultSearchIcon = () => (
      <svg
        className="h-5 w-5 text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
        />
      </svg>
    );

    const DefaultClearIcon = () => (
      <svg
        className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    );

    return (
      <div className="space-y-1">
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              "block text-sm leading-6 font-medium",
              hasError
                ? "text-red-700 dark:text-red-400"
                : "text-gray-900 dark:text-gray-100",
              required && "after:ml-0.5 after:text-red-500 after:content-['*']",
            )}
          >
            {label}
          </label>
        )}

        <div className="relative">
          {/* Search Icon */}
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            {searchIcon || <DefaultSearchIcon />}
          </div>

          {/* Input */}
          <input
            ref={ref}
            id={inputId}
            name={name}
            type="search"
            value={currentValue}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            className={inputClasses}
            onChange={handleChange}
            onBlur={onBlur}
            aria-invalid={hasError}
            aria-describedby={
              error
                ? `${inputId}-error`
                : helpText
                  ? `${inputId}-help`
                  : undefined
            }
            {...props}
          />

          {/* Clear Button */}
          {showClearButton && currentValue && !disabled && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <button
                type="button"
                onClick={handleClear}
                className="rounded-full p-1 transition-colors hover:bg-gray-100 dark:hover:bg-gray-600"
                aria-label="Clear search"
              >
                {clearIcon || <DefaultClearIcon />}
              </button>
            </div>
          )}
        </div>

        {error && (
          <p
            id={`${inputId}-error`}
            className="text-sm text-red-600 dark:text-red-400"
            role="alert"
          >
            {error}
          </p>
        )}

        {helpText && !error && (
          <p
            id={`${inputId}-help`}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {helpText}
          </p>
        )}
      </div>
    );
  },
);

SearchInput.displayName = "SearchInput";

// ============================================================================
// LAYOUT COMPONENTS
// ============================================================================

export const FormGroup = forwardRef<HTMLDivElement, FormGroupProps>(
  ({ children, className = "" }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-1", className)}>
        {children}
      </div>
    );
  },
);

FormGroup.displayName = "FormGroup";

export const FormSection = forwardRef<HTMLDivElement, FormSectionProps>(
  ({ title, description, children, className = "" }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-6", className)}>
        {(title || description) && (
          <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
            {title && (
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                {title}
              </h3>
            )}
            {description && (
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {description}
              </p>
            )}
          </div>
        )}
        <div className="space-y-6">{children}</div>
      </div>
    );
  },
);

FormSection.displayName = "FormSection";

export const FormGrid = forwardRef<HTMLDivElement, FormGridProps>(
  ({ children, cols = 1, gap = "md", className = "" }, ref) => {
    const gridClasses = {
      1: "grid-cols-1",
      2: "grid-cols-1 sm:grid-cols-2",
      3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",
      6: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",
      12: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-12",
    };

    const gapClasses = {
      sm: "gap-3",
      md: "gap-6",
      lg: "gap-8",
    };

    return (
      <div
        ref={ref}
        className={cn("grid", gridClasses[cols], gapClasses[gap], className)}
      >
        {children}
      </div>
    );
  },
);

FormGrid.displayName = "FormGrid";

export const FormCard = forwardRef<HTMLDivElement, FormCardProps>(
  ({ children, title, description, className = "" }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "rounded-lg bg-white p-6 shadow dark:bg-gray-800",
          className,
        )}
      >
        {(title || description) && (
          <div className="mb-6">
            {title && (
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {title}
              </h3>
            )}
            {description && (
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {description}
              </p>
            )}
          </div>
        )}
        {children}
      </div>
    );
  },
);

FormCard.displayName = "FormCard";

export const FormActions = forwardRef<HTMLDivElement, FormActionsProps>(
  ({ children, align = "right", className = "" }, ref) => {
    const alignClasses = {
      left: "justify-start",
      right: "justify-end",
      center: "justify-center",
      between: "justify-between",
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center space-x-3",
          alignClasses[align],
          className,
        )}
      >
        {children}
      </div>
    );
  },
);

FormActions.displayName = "FormActions";

export const FormDivider = forwardRef<HTMLDivElement, FormDividerProps>(
  ({ label, className = "" }, ref) => {
    return (
      <div ref={ref} className={cn("relative", className)}>
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300 dark:border-gray-600" />
        </div>
        {label && (
          <div className="relative flex justify-center text-sm">
            <span className="bg-white px-2 text-gray-500 dark:bg-gray-800 dark:text-gray-400">
              {label}
            </span>
          </div>
        )}
      </div>
    );
  },
);

FormDivider.displayName = "FormDivider";
