{"name": "flowbite-react-template-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier . --write", "format:check": "prettier . --check", "postinstall": "flowbite-react patch"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.9.0", "clsx": "^2.1.1", "flowbite": "^3.1.2", "flowbite-react": "^0.11.7", "next": "^15.3.0", "prisma": "^6.9.0", "query-string": "^9.1.2", "react": "^19.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-hook-form": "^7.56.2", "react-icons": "^5.5.0", "tailwind-merge": "^3.2.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.4", "@types/node": "^20.17.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.24.0", "eslint-config-next": "^15.3.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.4", "typescript": "^5.8.3"}}